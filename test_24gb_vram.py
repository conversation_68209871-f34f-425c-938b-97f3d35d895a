#!/usr/bin/env python3
"""
Script test đặc biệt cho máy 24GB VRAM
Kiểm tra các cấu hình tối ưu cho Qwen2.5 14B
"""

import torch
import time
import gc
from project_analyzer import ProjectAnalyzer

def check_vram():
    """Kiểm tra VRAM hiện tại"""
    if torch.cuda.is_available():
        device = torch.cuda.current_device()
        total = torch.cuda.get_device_properties(device).total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(device) / 1024**3
        cached = torch.cuda.memory_reserved(device) / 1024**3
        free = total - cached
        
        print(f"🔧 VRAM Status:")
        print(f"   Total: {total:.1f}GB")
        print(f"   Allocated: {allocated:.1f}GB")
        print(f"   Cached: {cached:.1f}GB") 
        print(f"   Free: {free:.1f}GB")
        return total, allocated, free
    else:
        print("❌ CUDA không có sẵn")
        return 0, 0, 0

def clear_memory():
    """Dọn dẹp memory"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def test_model_loading(model_name, use_quantization=False):
    """Test tải model với các cấu hình khác nhau"""
    print(f"\n{'='*60}")
    print(f"🧪 TEST: {model_name}")
    print(f"📊 Quantization: {'Có' if use_quantization else 'Không'}")
    print(f"{'='*60}")
    
    # Kiểm tra VRAM trước khi tải
    print("📋 VRAM trước khi tải model:")
    total, allocated_before, free_before = check_vram()
    
    if total < 20:
        print("⚠️  VRAM < 20GB, khuyến nghị sử dụng quantization")
    
    try:
        start_time = time.time()
        
        # Khởi tạo analyzer
        analyzer = ProjectAnalyzer(model_name, use_quantization)
        
        # Tải model
        print("\n🔄 Đang tải model...")
        analyzer.load_model()
        
        load_time = time.time() - start_time
        
        # Kiểm tra VRAM sau khi tải
        print(f"\n📋 VRAM sau khi tải model:")
        _, allocated_after, free_after = check_vram()
        
        memory_used = allocated_after - allocated_before
        
        print(f"\n✅ Kết quả:")
        print(f"   ⏱️  Thời gian tải: {load_time:.1f}s")
        print(f"   💾 Memory sử dụng: {memory_used:.1f}GB")
        print(f"   🆓 VRAM còn lại: {free_after:.1f}GB")
        
        # Test inference đơn giản
        print(f"\n🧪 Test inference...")
        test_prompt = "Phân tích dự án Python Flask với 10 files."
        
        start_inference = time.time()
        
        # Tạo input đơn giản
        messages = [
            {"role": "system", "content": "Bạn là chuyên gia phân tích code."},
            {"role": "user", "content": test_prompt}
        ]
        
        text = analyzer.tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        inputs = analyzer.tokenizer([text], return_tensors="pt").to(analyzer.device)
        
        with torch.no_grad():
            outputs = analyzer.model.generate(
                inputs.input_ids,
                max_new_tokens=100,  # Giới hạn để test nhanh
                do_sample=True,
                temperature=0.7,
                pad_token_id=analyzer.tokenizer.eos_token_id
            )
        
        inference_time = time.time() - start_inference
        
        # Decode response
        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, outputs)
        ]
        response = analyzer.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        
        print(f"   ⏱️  Thời gian inference: {inference_time:.1f}s")
        print(f"   📝 Response preview: {response[:100]}...")
        
        # Kiểm tra VRAM cuối cùng
        print(f"\n📋 VRAM sau inference:")
        check_vram()
        
        return True, memory_used, load_time, inference_time
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False, 0, 0, 0
    
    finally:
        # Dọn dẹp
        if 'analyzer' in locals():
            del analyzer
        clear_memory()

def main():
    print("🚀 TEST QWEN2.5 14B TRÊN MÁY 24GB VRAM")
    print("="*60)
    
    # Kiểm tra VRAM ban đầu
    total, _, _ = check_vram()
    
    if total < 24:
        print(f"⚠️  VRAM hiện tại: {total:.1f}GB < 24GB")
        print("   Vẫn có thể chạy nhưng cần tối ưu")
    else:
        print(f"✅ VRAM: {total:.1f}GB >= 24GB - Tuyệt vời!")
    
    # Danh sách test cases
    test_cases = [
        ("Qwen/Qwen2.5-14B-Instruct", False),  # FP16 standard
        ("Qwen/Qwen2.5-14B-Instruct", True),   # 4-bit quantization
        ("Qwen/Qwen2.5-7B-Instruct", False),   # Model nhỏ hơn để so sánh
    ]
    
    results = []
    
    for model_name, use_quantization in test_cases:
        success, memory_used, load_time, inference_time = test_model_loading(
            model_name, use_quantization
        )
        
        results.append({
            "model": model_name,
            "quantization": use_quantization,
            "success": success,
            "memory_used": memory_used,
            "load_time": load_time,
            "inference_time": inference_time
        })
        
        # Nghỉ giữa các test
        print("\n⏸️  Nghỉ 5s trước test tiếp theo...")
        time.sleep(5)
    
    # Tổng kết
    print(f"\n{'='*60}")
    print("📊 TỔNG KẾT KẾT QUẢ")
    print(f"{'='*60}")
    
    for result in results:
        model_short = result["model"].split("/")[-1]
        quant_str = " + 4bit" if result["quantization"] else ""
        status = "✅" if result["success"] else "❌"
        
        print(f"{status} {model_short}{quant_str}")
        if result["success"]:
            print(f"   💾 Memory: {result['memory_used']:.1f}GB")
            print(f"   ⏱️  Load: {result['load_time']:.1f}s")
            print(f"   🚀 Inference: {result['inference_time']:.1f}s")
        print()
    
    # Khuyến nghị
    print("💡 KHUYẾN NGHỊ CHO MÁY 24GB VRAM:")
    
    successful_configs = [r for r in results if r["success"]]
    
    if successful_configs:
        # Tìm config tốt nhất (ít memory nhất mà vẫn chạy được)
        best_config = min(successful_configs, key=lambda x: x["memory_used"])
        
        model_short = best_config["model"].split("/")[-1]
        quant_str = " với quantization" if best_config["quantization"] else ""
        
        print(f"🏆 Tốt nhất: {model_short}{quant_str}")
        print(f"   💾 Chỉ dùng {best_config['memory_used']:.1f}GB VRAM")
        print(f"   ⏱️  Load trong {best_config['load_time']:.1f}s")
        
        # Lệnh chạy khuyến nghị
        quant_flag = " -q" if best_config["quantization"] else ""
        model_flag = f" -m {best_config['model']}" if best_config['model'] != "Qwen/Qwen2.5-14B-Instruct" else ""
        
        print(f"\n🚀 Lệnh chạy khuyến nghị:")
        print(f"   python project_analyzer.py /path/to/project{model_flag}{quant_flag}")
        
    else:
        print("❌ Không có config nào chạy được")
        print("   Thử giảm model size hoặc tăng swap memory")

if __name__ == "__main__":
    main()
