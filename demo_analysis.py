#!/usr/bin/env python3
"""
Demo script để phân tích dự án mẫu
"""

import os
import tempfile
from pathlib import Path
from project_analyzer import ProjectAnalyzer

def create_sample_project():
    """
    Tạo một dự án mẫu để demo
    """
    # Tạo thư mục tạm
    temp_dir = tempfile.mkdtemp(prefix="sample_project_")
    project_path = Path(temp_dir)
    
    # Tạo cấu trúc dự án mẫu
    (project_path / "src").mkdir()
    (project_path / "tests").mkdir()
    (project_path / "docs").mkdir()
    (project_path / "config").mkdir()
    
    # Tạo file README.md
    readme_content = """# Sample Project

Đây là một dự án mẫu để demo phân tích code.

## Tính năng
- Web API sử dụng Flask
- Database với SQLAlchemy
- Authentication với JWT
- Unit tests với pytest

## Cài đặt
```bash
pip install -r requirements.txt
python app.py
```
"""
    (project_path / "README.md").write_text(readme_content, encoding='utf-8')
    
    # Tạo file app.py
    app_content = """from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string'

db = SQLAlchemy(app)
jwt = JWTManager(app)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    
    def __repr__(self):
        return f'<User {self.username}>'

@app.route('/api/login', methods=['POST'])
def login():
    username = request.json.get('username', None)
    password = request.json.get('password', None)
    
    # Simplified authentication
    if username == 'admin' and password == 'password':
        access_token = create_access_token(identity=username)
        return jsonify(access_token=access_token)
    
    return jsonify({"msg": "Bad username or password"}), 401

@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    users = User.query.all()
    return jsonify([{'id': u.id, 'username': u.username, 'email': u.email} for u in users])

@app.route('/api/users', methods=['POST'])
@jwt_required()
def create_user():
    data = request.get_json()
    user = User(username=data['username'], email=data['email'])
    db.session.add(user)
    db.session.commit()
    return jsonify({'message': 'User created successfully'}), 201

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
"""
    (project_path / "app.py").write_text(app_content, encoding='utf-8')
    
    # Tạo file requirements.txt cho dự án mẫu
    requirements_content = """Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.2
pytest==7.4.2
requests==2.31.0
"""
    (project_path / "requirements.txt").write_text(requirements_content, encoding='utf-8')
    
    # Tạo file config
    config_content = """{
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "myapp"
    },
    "jwt": {
        "expiration": 3600,
        "algorithm": "HS256"
    },
    "logging": {
        "level": "INFO",
        "file": "app.log"
    }
}"""
    (project_path / "config" / "config.json").write_text(config_content, encoding='utf-8')
    
    # Tạo file test
    test_content = """import pytest
import json
from app import app, db, User

@pytest.fixture
def client():
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    with app.test_client() as client:
        with app.app_context():
            db.create_all()
        yield client

def test_login_success(client):
    response = client.post('/api/login', 
                          json={'username': 'admin', 'password': 'password'})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert 'access_token' in data

def test_login_failure(client):
    response = client.post('/api/login', 
                          json={'username': 'wrong', 'password': 'wrong'})
    assert response.status_code == 401

def test_get_users_unauthorized(client):
    response = client.get('/api/users')
    assert response.status_code == 401

def test_create_user_unauthorized(client):
    response = client.post('/api/users', 
                          json={'username': 'test', 'email': '<EMAIL>'})
    assert response.status_code == 401
"""
    (project_path / "tests" / "test_app.py").write_text(test_content, encoding='utf-8')
    
    # Tạo file utils
    utils_content = """import hashlib
import secrets
from datetime import datetime, timedelta

def hash_password(password: str) -> str:
    \"\"\"Hash password using SHA-256\"\"\"
    salt = secrets.token_hex(16)
    return hashlib.sha256((password + salt).encode()).hexdigest() + ':' + salt

def verify_password(password: str, hashed: str) -> bool:
    \"\"\"Verify password against hash\"\"\"
    password_hash, salt = hashed.split(':')
    return hashlib.sha256((password + salt).encode()).hexdigest() == password_hash

def generate_token() -> str:
    \"\"\"Generate random token\"\"\"
    return secrets.token_urlsafe(32)

class DateTimeHelper:
    @staticmethod
    def now():
        return datetime.now()
    
    @staticmethod
    def add_hours(dt: datetime, hours: int):
        return dt + timedelta(hours=hours)
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S"):
        return dt.strftime(format_str)
"""
    (project_path / "src" / "utils.py").write_text(utils_content, encoding='utf-8')
    
    return str(project_path)

def main():
    print("🚀 Demo Phân tích Dự án với Qwen2.5 14B")
    print("="*50)
    
    # Tạo dự án mẫu
    print("📁 Đang tạo dự án mẫu...")
    sample_project_path = create_sample_project()
    print(f"✅ Dự án mẫu đã được tạo tại: {sample_project_path}")
    
    try:
        # Khởi tạo analyzer
        print("\n🤖 Đang khởi tạo AI Analyzer...")
        analyzer = ProjectAnalyzer()
        
        print("📥 Đang tải model Qwen2.5 14B...")
        print("⚠️  Lưu ý: Quá trình này có thể mất vài phút lần đầu tiên...")
        analyzer.load_model()
        
        # Phân tích dự án
        print(f"\n🔍 Đang phân tích dự án...")
        result = analyzer.analyze_project(sample_project_path, "analysis_result.json")
        
        # Hiển thị kết quả
        print("\n" + "="*80)
        print("📊 KẾT QUẢ PHÂN TÍCH DỰ ÁN")
        print("="*80)
        print(f"📁 Dự án: {result['project_structure']['project_name']}")
        print(f"📅 Thời gian: {result['timestamp']}")
        print(f"📄 Tổng số file: {result['project_structure']['total_files']}")
        print(f"💻 File code: {len(result['project_structure']['code_files'])}")
        print(f"⚙️  File config: {len(result['project_structure']['config_files'])}")
        print(f"📚 File docs: {len(result['project_structure']['documentation'])}")
        
        print("\n" + "-"*80)
        print("🤖 PHÂN TÍCH TỪ AI:")
        print("-"*80)
        print(result['ai_analysis'])
        
        print(f"\n💾 Kết quả chi tiết đã được lưu vào: analysis_result.json")
        print(f"🗂️  Dự án mẫu tại: {sample_project_path}")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
