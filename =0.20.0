Collecting accelerate
  Downloading accelerate-1.10.1-py3-none-any.whl.metadata (19 kB)
Requirement already satisfied: numpy<3.0.0,>=1.17 in /venv/analysis_code/lib/python3.10/site-packages (from accelerate) (2.1.2)
Requirement already satisfied: packaging>=20.0 in /venv/analysis_code/lib/python3.10/site-packages (from accelerate) (25.0)
Collecting psutil (from accelerate)
  Downloading psutil-7.1.0-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (23 kB)
Requirement already satisfied: pyyaml in /venv/analysis_code/lib/python3.10/site-packages (from accelerate) (6.0.2)
Requirement already satisfied: torch>=2.0.0 in /venv/analysis_code/lib/python3.10/site-packages (from accelerate) (2.5.1+cu121)
Requirement already satisfied: huggingface_hub>=0.21.0 in /venv/analysis_code/lib/python3.10/site-packages (from accelerate) (0.35.0)
Requirement already satisfied: safetensors>=0.4.3 in /venv/analysis_code/lib/python3.10/site-packages (from accelerate) (0.6.2)
Requirement already satisfied: filelock in /venv/analysis_code/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate) (3.13.1)
Requirement already satisfied: fsspec>=2023.5.0 in /venv/analysis_code/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate) (2024.6.1)
Requirement already satisfied: requests in /venv/analysis_code/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate) (2.32.5)
Requirement already satisfied: tqdm>=4.42.1 in /venv/analysis_code/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate) (4.67.1)
Requirement already satisfied: typing-extensions>=3.7.4.3 in /venv/analysis_code/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate) (4.12.2)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /venv/analysis_code/lib/python3.10/site-packages (from huggingface_hub>=0.21.0->accelerate) (1.1.10)
Requirement already satisfied: networkx in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (3.3)
Requirement already satisfied: jinja2 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (3.1.4)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (12.1.105)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (12.1.105)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (12.1.105)
Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (9.1.0.70)
Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (12.1.3.1)
Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (11.0.2.54)
Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (10.3.2.106)
Requirement already satisfied: nvidia-cusolver-cu12==11.4.5.107 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (11.4.5.107)
Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (12.1.0.106)
Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (2.21.5)
Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (12.1.105)
Requirement already satisfied: triton==3.1.0 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (3.1.0)
Requirement already satisfied: sympy==1.13.1 in /venv/analysis_code/lib/python3.10/site-packages (from torch>=2.0.0->accelerate) (1.13.1)
Requirement already satisfied: nvidia-nvjitlink-cu12 in /venv/analysis_code/lib/python3.10/site-packages (from nvidia-cusolver-cu12==11.4.5.107->torch>=2.0.0->accelerate) (12.9.86)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /venv/analysis_code/lib/python3.10/site-packages (from sympy==1.13.1->torch>=2.0.0->accelerate) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in /venv/analysis_code/lib/python3.10/site-packages (from jinja2->torch>=2.0.0->accelerate) (2.1.5)
Requirement already satisfied: charset_normalizer<4,>=2 in /venv/analysis_code/lib/python3.10/site-packages (from requests->huggingface_hub>=0.21.0->accelerate) (3.4.3)
Requirement already satisfied: idna<4,>=2.5 in /venv/analysis_code/lib/python3.10/site-packages (from requests->huggingface_hub>=0.21.0->accelerate) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /venv/analysis_code/lib/python3.10/site-packages (from requests->huggingface_hub>=0.21.0->accelerate) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in /venv/analysis_code/lib/python3.10/site-packages (from requests->huggingface_hub>=0.21.0->accelerate) (2025.8.3)
Downloading accelerate-1.10.1-py3-none-any.whl (374 kB)
Downloading psutil-7.1.0-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (291 kB)
Installing collected packages: psutil, accelerate

Successfully installed accelerate-1.10.1 psutil-7.1.0
