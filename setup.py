#!/usr/bin/env python3
"""
Setup script để kiểm tra và cài đặt dependencies
"""

import subprocess
import sys
import importlib
import platform

def check_python_version():
    """Kiểm tra phiên bản Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Cần Python 3.8 trở lên")
        print(f"   Phiên bản hiện tại: {version.major}.{version.minor}.{version.micro}")
        return False
    else:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True

def check_gpu():
    """Kiểm tra GPU và CUDA"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU: {gpu_name}")
            print(f"   Số GPU: {gpu_count}")
            print(f"   VRAM: {memory:.1f}GB")
            
            if memory < 16:
                print("⚠️  Cảnh báo: VRAM < 16GB, khuyến nghị sử dụng model 7B")
            
            return True
        else:
            print("⚠️  Không tìm thấy GPU, sẽ sử dụng CPU")
            return False
    except ImportError:
        print("⚠️  PyTorch chưa được cài đặt")
        return False

def check_memory():
    """Kiểm tra RAM"""
    try:
        import psutil
        memory = psutil.virtual_memory()
        total_gb = memory.total / 1024**3
        available_gb = memory.available / 1024**3
        
        print(f"💾 RAM: {total_gb:.1f}GB total, {available_gb:.1f}GB available")
        
        if total_gb < 16:
            print("⚠️  Cảnh báo: RAM < 16GB, có thể gặp vấn đề với model 14B")
            return False
        return True
    except ImportError:
        print("⚠️  Không thể kiểm tra RAM (psutil chưa cài)")
        return True

def install_requirements():
    """Cài đặt requirements"""
    print("\n📦 Đang cài đặt dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Đã cài đặt thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi cài đặt: {e}")
        return False

def check_dependencies():
    """Kiểm tra các package đã cài đặt"""
    required_packages = [
        "torch",
        "transformers", 
        "accelerate",
        "sentencepiece"
    ]
    
    missing = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - chưa cài đặt")
            missing.append(package)
    
    return len(missing) == 0

def test_model_loading():
    """Test tải model"""
    print("\n🧪 Đang test tải model...")
    try:
        from transformers import AutoTokenizer
        
        # Test với model nhỏ trước
        model_name = "Qwen/Qwen2.5-0.5B-Instruct"
        print(f"   Đang test với {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("✅ Có thể tải tokenizer")
        
        # Test tokenize
        text = "Hello world"
        tokens = tokenizer(text, return_tensors="pt")
        print("✅ Tokenization hoạt động")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test model: {e}")
        return False

def main():
    print("🔧 KIỂM TRA HỆ THỐNG CHO PROJECT ANALYZER")
    print("=" * 50)
    
    # Kiểm tra Python
    if not check_python_version():
        return 1
    
    # Kiểm tra OS
    print(f"🖥️  OS: {platform.system()} {platform.release()}")
    
    # Kiểm tra dependencies
    print(f"\n📋 Kiểm tra dependencies:")
    deps_ok = check_dependencies()
    
    if not deps_ok:
        print(f"\n❓ Bạn có muốn cài đặt dependencies? (y/n): ", end="")
        response = input().lower().strip()
        if response in ['y', 'yes', 'có']:
            if not install_requirements():
                return 1
            deps_ok = check_dependencies()
    
    if not deps_ok:
        print("❌ Vẫn còn dependencies chưa được cài đặt")
        return 1
    
    # Kiểm tra hardware
    print(f"\n🔧 Kiểm tra hardware:")
    check_memory()
    gpu_available = check_gpu()
    
    # Test model
    if not test_model_loading():
        print("⚠️  Có vấn đề với việc tải model")
    
    # Khuyến nghị
    print(f"\n💡 KHUYẾN NGHỊ:")
    if gpu_available:
        print("✅ Hệ thống sẵn sàng sử dụng GPU")
        print("   Có thể sử dụng model Qwen2.5-14B-Instruct")
    else:
        print("⚠️  Không có GPU, sẽ sử dụng CPU")
        print("   Khuyến nghị sử dụng model Qwen2.5-7B-Instruct")
        print("   Hoặc Qwen2.5-0.5B-Instruct cho test nhanh")
    
    print(f"\n🚀 Để bắt đầu:")
    print("   python demo_analysis.py")
    print("   hoặc")
    print("   python project_analyzer.py /path/to/your/project")
    
    return 0

if __name__ == "__main__":
    exit(main())
