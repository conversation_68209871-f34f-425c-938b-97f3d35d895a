# Project Analyzer với Qwen2.5 14B

Công cụ phân tích dự án sử dụng model AI Qwen2.5 14B từ Hugging Face để đưa ra các insights chi tiết về codebase.

## 🚀 Tính năng

- **Quét cấu trúc dự án**: Phân tích toàn bộ cấu trúc thư mục và file
- **Phân loại file**: Tự động phân loại code, config, documentation
- **Phân tích AI**: Sử dụng Qwen2.5 14B để đưa ra đánh giá chuyên sâu
- **Báo cáo chi tiết**: Xuất kết quả dưới dạng JSON và text
- **Hỗ trợ đa ngôn ngữ**: Python, JavaScript, Java, C++, và nhiều ngôn ngữ khác

## 📋 Yêu cầu hệ thống

- Python 3.8+
- GPU với ít nhất 16GB VRAM (khuyến nghị) hoặc 32GB+ RAM cho CPU
- Kết nối internet để tải model lần đầu

## 🛠️ Cài đặt

1. **Clone repository**:
```bash
git clone <repository-url>
cd agent_analysis_code_local
```

2. **Cài đặt dependencies**:
```bash
pip install -r requirements.txt
```

3. **Chạy demo**:
```bash
python demo_analysis.py
```

## 📖 Cách sử dụng

### 1. Phân tích dự án cơ bản

```bash
python project_analyzer.py /path/to/your/project
```

### 2. Lưu kết quả vào file

```bash
python project_analyzer.py /path/to/your/project -o analysis_result.json
```

### 3. Sử dụng model khác

```bash
python project_analyzer.py /path/to/your/project -m "Qwen/Qwen2.5-7B-Instruct"
```

### 4. Sử dụng trong code Python

```python
from project_analyzer import ProjectAnalyzer

# Khởi tạo analyzer
analyzer = ProjectAnalyzer("Qwen/Qwen2.5-14B-Instruct")
analyzer.load_model()

# Phân tích dự án
result = analyzer.analyze_project("/path/to/project", "output.json")

# Xem kết quả
print(result['ai_analysis'])
```

## 📊 Kết quả phân tích

Tool sẽ cung cấp:

1. **Loại dự án**: Web app, mobile app, library, tool, etc.
2. **Công nghệ sử dụng**: Framework, library, ngôn ngữ chính
3. **Kiến trúc**: Mô tả kiến trúc tổng thể
4. **Chất lượng code**: Đánh giá cấu trúc và tổ chức
5. **Điểm mạnh**: Những ưu điểm của dự án
6. **Điểm cần cải thiện**: Vấn đề và cơ hội cải thiện
7. **Khuyến nghị**: Đề xuất phát triển tiếp theo

## 🔧 Tùy chỉnh

### Thay đổi model

Bạn có thể sử dụng các model Qwen khác:

- `Qwen/Qwen2.5-7B-Instruct` (nhẹ hơn)
- `Qwen/Qwen2.5-14B-Instruct` (cân bằng)
- `Qwen/Qwen2.5-32B-Instruct` (mạnh nhất)

### Tùy chỉnh prompt

Chỉnh sửa method `generate_analysis_prompt()` trong `ProjectAnalyzer` class để thay đổi cách phân tích.

### Thêm loại file mới

Cập nhật các set `code_extensions`, `config_extensions`, `doc_extensions` trong method `scan_project_structure()`.

## 📁 Cấu trúc file

```
.
├── project_analyzer.py    # Main analyzer class
├── demo_analysis.py       # Demo script
├── requirements.txt       # Python dependencies
├── README.md             # Hướng dẫn này
└── analysis_result.json  # Kết quả phân tích (sau khi chạy)
```

## ⚡ Tối ưu hiệu suất

### Sử dụng GPU
```python
# Tự động detect GPU
analyzer = ProjectAnalyzer()
analyzer.load_model()  # Sẽ dùng GPU nếu có
```

### Giảm memory usage
```python
# Sử dụng model nhỏ hơn
analyzer = ProjectAnalyzer("Qwen/Qwen2.5-7B-Instruct")

# Hoặc force sử dụng CPU
import torch
torch.cuda.is_available = lambda: False
```

## 🐛 Troubleshooting

### Lỗi memory
- Sử dụng model 7B thay vì 14B
- Đóng các ứng dụng khác
- Tăng swap space

### Lỗi tải model
- Kiểm tra kết nối internet
- Xóa cache: `rm -rf ~/.cache/huggingface/`
- Cập nhật transformers: `pip install -U transformers`

### Lỗi CUDA
- Cài đặt PyTorch với CUDA: `pip install torch --index-url https://download.pytorch.org/whl/cu118`
- Kiểm tra driver GPU

## 📝 Ví dụ kết quả

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "project_structure": {
    "project_name": "my_web_app",
    "total_files": 45,
    "file_types": {
      ".py": 15,
      ".js": 12,
      ".html": 8,
      ".css": 5,
      ".json": 3,
      ".md": 2
    }
  },
  "ai_analysis": "Đây là một ứng dụng web Flask với kiến trúc MVC..."
}
```

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Tạo Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 🙏 Acknowledgments

- [Qwen Team](https://github.com/QwenLM/Qwen) cho model AI tuyệt vời
- [Hugging Face](https://huggingface.co/) cho platform và transformers library
- Cộng đồng open source
