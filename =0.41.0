Collecting bitsandbytes
  Downloading bitsandbytes-0.47.0-py3-none-manylinux_2_24_x86_64.whl.metadata (11 kB)
Requirement already satisfied: torch<3,>=2.2 in /venv/analysis_code/lib/python3.10/site-packages (from bitsandbytes) (2.5.1+cu121)
Requirement already satisfied: numpy>=1.17 in /venv/analysis_code/lib/python3.10/site-packages (from bitsandbytes) (2.1.2)
Requirement already satisfied: filelock in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.13.1)
Requirement already satisfied: typing-extensions>=4.8.0 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (4.12.2)
Requirement already satisfied: networkx in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.3)
Requirement already satisfied: jinja2 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.1.4)
Requirement already satisfied: fsspec in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (2024.6.1)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.1.105)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.1.105)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.1.105)
Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (9.1.0.70)
Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.1.3.1)
Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (11.0.2.54)
Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (10.3.2.106)
Requirement already satisfied: nvidia-cusolver-cu12==11.4.5.107 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (11.4.5.107)
Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.1.0.106)
Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (2.21.5)
Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.1.105)
Requirement already satisfied: triton==3.1.0 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.1.0)
Requirement already satisfied: sympy==1.13.1 in /venv/analysis_code/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (1.13.1)
Requirement already satisfied: nvidia-nvjitlink-cu12 in /venv/analysis_code/lib/python3.10/site-packages (from nvidia-cusolver-cu12==11.4.5.107->torch<3,>=2.2->bitsandbytes) (12.9.86)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /venv/analysis_code/lib/python3.10/site-packages (from sympy==1.13.1->torch<3,>=2.2->bitsandbytes) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in /venv/analysis_code/lib/python3.10/site-packages (from jinja2->torch<3,>=2.2->bitsandbytes) (2.1.5)
Downloading bitsandbytes-0.47.0-py3-none-manylinux_2_24_x86_64.whl (61.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 61.3/61.3 MB 143.5 MB/s  0:00:00
