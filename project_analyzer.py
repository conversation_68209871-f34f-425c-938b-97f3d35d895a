#!/usr/bin/env python3
"""
Project Analyzer using Qwen2.5 14B Model
Phân tích dự án sử dụng model Qwen2.5 14B từ Hugging Face
"""

import os
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any
import logging
from datetime import datetime

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
except ImportError:
    print("Cần cài đặt transformers và torch:")
    print("pip install transformers torch")
    exit(1)

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProjectAnalyzer:
    def __init__(self, model_name="Qwen/Qwen2.5-14B-Instruct"):
        """
        Khởi tạo Project Analyzer với model Qwen2.5 14B
        """
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Sử dụng device: {self.device}")
        
    def load_model(self):
        """Tải model và tokenizer"""
        try:
            logger.info(f"Đang tải model {self.model_name}...")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Cấu hình model với các tùy chọn tối ưu
            model_kwargs = {
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "device_map": "auto" if self.device == "cuda" else None,
                "trust_remote_code": True
            }
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name, 
                **model_kwargs
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
                
            logger.info("Model đã được tải thành công!")
            
        except Exception as e:
            logger.error(f"Lỗi khi tải model: {e}")
            raise

    def scan_project_structure(self, project_path: str) -> Dict[str, Any]:
        """
        Quét cấu trúc dự án và thu thập thông tin
        """
        project_path = Path(project_path)
        if not project_path.exists():
            raise FileNotFoundError(f"Đường dẫn không tồn tại: {project_path}")
            
        structure = {
            "project_name": project_path.name,
            "total_files": 0,
            "file_types": {},
            "directories": [],
            "files": [],
            "code_files": [],
            "config_files": [],
            "documentation": []
        }
        
        # Các extension code phổ biến
        code_extensions = {
            '.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.php', 
            '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.r', '.m', '.mm'
        }
        
        # Các file config phổ biến
        config_extensions = {
            '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.xml'
        }
        
        # Các file documentation
        doc_extensions = {
            '.md', '.txt', '.rst', '.adoc'
        }
        
        for root, dirs, files in os.walk(project_path):
            # Bỏ qua các thư mục ẩn và node_modules, __pycache__, etc.
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv', 'env']]
            
            rel_root = os.path.relpath(root, project_path)
            if rel_root != '.':
                structure["directories"].append(rel_root)
            
            for file in files:
                if file.startswith('.'):
                    continue
                    
                file_path = os.path.join(root, file)
                rel_file_path = os.path.relpath(file_path, project_path)
                
                structure["files"].append(rel_file_path)
                structure["total_files"] += 1
                
                # Phân loại file theo extension
                ext = Path(file).suffix.lower()
                structure["file_types"][ext] = structure["file_types"].get(ext, 0) + 1
                
                if ext in code_extensions:
                    structure["code_files"].append(rel_file_path)
                elif ext in config_extensions or file.lower() in ['dockerfile', 'makefile', 'requirements.txt']:
                    structure["config_files"].append(rel_file_path)
                elif ext in doc_extensions or file.lower() in ['readme', 'license', 'changelog']:
                    structure["documentation"].append(rel_file_path)
        
        return structure

    def read_file_content(self, file_path: str, max_lines: int = 100) -> str:
        """
        Đọc nội dung file với giới hạn số dòng
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if len(lines) > max_lines:
                    content = ''.join(lines[:max_lines])
                    content += f"\n... (file có {len(lines)} dòng, chỉ hiển thị {max_lines} dòng đầu)"
                else:
                    content = ''.join(lines)
                return content
        except Exception as e:
            return f"Không thể đọc file: {e}"

    def generate_analysis_prompt(self, structure: Dict[str, Any], sample_files: Dict[str, str]) -> str:
        """
        Tạo prompt để phân tích dự án
        """
        prompt = f"""Bạn là một chuyên gia phân tích code. Hãy phân tích dự án sau đây và đưa ra đánh giá chi tiết:

## Thông tin dự án:
- Tên dự án: {structure['project_name']}
- Tổng số file: {structure['total_files']}
- Số thư mục: {len(structure['directories'])}

## Cấu trúc file:
- File code: {len(structure['code_files'])} files
- File config: {len(structure['config_files'])} files  
- File documentation: {len(structure['documentation'])} files

## Các loại file:
{json.dumps(structure['file_types'], indent=2, ensure_ascii=False)}

## Mẫu nội dung một số file quan trọng:
"""
        
        for file_path, content in sample_files.items():
            prompt += f"\n### File: {file_path}\n```\n{content[:1000]}...\n```\n"
        
        prompt += """
## Yêu cầu phân tích:
1. **Loại dự án**: Xác định đây là loại dự án gì (web app, mobile app, library, tool, etc.)
2. **Công nghệ sử dụng**: Liệt kê các framework, library, ngôn ngữ lập trình chính
3. **Kiến trúc**: Mô tả kiến trúc tổng thể của dự án
4. **Chất lượng code**: Đánh giá về cấu trúc, tổ chức code
5. **Điểm mạnh**: Những điểm tốt của dự án
6. **Điểm cần cải thiện**: Những vấn đề hoặc cơ hội cải thiện
7. **Khuyến nghị**: Đề xuất các bước tiếp theo để phát triển dự án

Hãy trả lời bằng tiếng Việt một cách chi tiết và có cấu trúc.
"""
        return prompt

    def analyze_with_ai(self, prompt: str) -> str:
        """
        Sử dụng model AI để phân tích
        """
        try:
            # Chuẩn bị input
            messages = [
                {"role": "system", "content": "Bạn là một chuyên gia phân tích code và kiến trúc phần mềm."},
                {"role": "user", "content": prompt}
            ]
            
            # Tokenize
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.device)
            
            # Generate response
            with torch.no_grad():
                generated_ids = self.model.generate(
                    model_inputs.input_ids,
                    max_new_tokens=2048,
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            return response
            
        except Exception as e:
            logger.error(f"Lỗi khi phân tích với AI: {e}")
            return f"Lỗi trong quá trình phân tích: {e}"

    def analyze_project(self, project_path: str, output_file: str = None) -> Dict[str, Any]:
        """
        Phân tích dự án hoàn chỉnh
        """
        logger.info(f"Bắt đầu phân tích dự án: {project_path}")
        
        # 1. Quét cấu trúc dự án
        structure = self.scan_project_structure(project_path)
        logger.info(f"Đã quét {structure['total_files']} files")
        
        # 2. Đọc nội dung một số file quan trọng
        sample_files = {}
        important_files = []
        
        # Ưu tiên các file quan trọng
        priority_files = ['README.md', 'package.json', 'requirements.txt', 'main.py', 'app.py', 'index.js', 'index.html']
        
        for file in priority_files:
            for code_file in structure['code_files'] + structure['config_files'] + structure['documentation']:
                if file.lower() in code_file.lower():
                    important_files.append(code_file)
                    break
        
        # Thêm một số file code khác
        remaining_files = [f for f in structure['code_files'][:5] if f not in important_files]
        important_files.extend(remaining_files)
        
        for file_path in important_files[:8]:  # Giới hạn 8 files
            full_path = os.path.join(project_path, file_path)
            content = self.read_file_content(full_path)
            sample_files[file_path] = content
        
        # 3. Tạo prompt và phân tích với AI
        prompt = self.generate_analysis_prompt(structure, sample_files)
        ai_analysis = self.analyze_with_ai(prompt)
        
        # 4. Tổng hợp kết quả
        result = {
            "timestamp": datetime.now().isoformat(),
            "project_structure": structure,
            "analyzed_files": list(sample_files.keys()),
            "ai_analysis": ai_analysis
        }
        
        # 5. Lưu kết quả
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"Kết quả đã được lưu vào: {output_file}")
        
        return result

def main():
    parser = argparse.ArgumentParser(description="Phân tích dự án sử dụng Qwen2.5 14B")
    parser.add_argument("project_path", help="Đường dẫn đến dự án cần phân tích")
    parser.add_argument("-o", "--output", help="File output để lưu kết quả (JSON)")
    parser.add_argument("-m", "--model", default="Qwen/Qwen2.5-14B-Instruct", help="Tên model Hugging Face")
    
    args = parser.parse_args()
    
    try:
        # Khởi tạo analyzer
        analyzer = ProjectAnalyzer(args.model)
        analyzer.load_model()
        
        # Phân tích dự án
        result = analyzer.analyze_project(args.project_path, args.output)
        
        # In kết quả
        print("\n" + "="*80)
        print("KẾT QUẢ PHÂN TÍCH DỰ ÁN")
        print("="*80)
        print(f"Dự án: {result['project_structure']['project_name']}")
        print(f"Thời gian phân tích: {result['timestamp']}")
        print("\n" + "-"*80)
        print("PHÂN TÍCH TỪ AI:")
        print("-"*80)
        print(result['ai_analysis'])
        
    except Exception as e:
        logger.error(f"Lỗi: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
